import { PrismaClient } from "@prisma/client"
import bcrypt from "bcryptjs"
import <PERSON>Auth from "next-auth"
import CredentialsProvider from "next-auth/providers/credentials"
import { z } from "zod"

// Instância do Prisma com configuração otimizada
const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['error'] : [],
  errorFormat: 'minimal'
})

// Schema de validação para login
const loginSchema = z.object({
  email: z.string().email("Email inválido"),
  password: z.string().min(6, "Senha deve ter pelo menos 6 caracteres")
})

export const { handlers, auth, signIn, signOut } = NextAuth({
  secret: process.env.AUTH_SECRET || "PxxYju+eBVgr2sOTXL9zBPmBSkH7lVYG2e9+WxnLbUg=",
  trustHost: true,
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: {
          label: "Email",
          type: "email",
          placeholder: "<EMAIL>"
        },
        password: {
          label: "Senha",
          type: "password"
        }
      },
      async authorize(credentials) {
        try {
          // Log apenas em desenvolvimento
          if (process.env.NODE_ENV === 'development') {
            console.log("🔄 Tentativa de login")
          }

          // Validar entrada
          const validatedFields = loginSchema.safeParse(credentials)

          if (!validatedFields.success) {
            if (process.env.NODE_ENV === 'development') {
              console.log("❌ Campos inválidos:", validatedFields.error.flatten().fieldErrors)
            }
            return null
          }

          const { email, password } = validatedFields.data

          // Tentar autenticação com banco de dados primeiro
          try {
            const user = await Promise.race([
              prisma.user.findUnique({
                where: {
                  email: email.toLowerCase(),
                  ativo: true
                }
              }),
              new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Database timeout')), 3000)
              )
            ])

            if (user && user.passwordHash) {
              const isValidPassword = await bcrypt.compare(password, user.passwordHash)

              if (isValidPassword) {
                if (process.env.NODE_ENV === 'development') {
                  console.log("✅ Login com banco bem-sucedido")
                }
                return {
                  id: user.id,
                  email: user.email,
                  name: user.nome,
                  nivelAcesso: user.nivelAcesso,
                  ativo: user.ativo
                }
              }
            }
          } catch (dbError) {
            // Se falhar conexão com banco, usar fallback
            if (process.env.NODE_ENV === 'development') {
              console.log("⚠️ Banco indisponível, usando fallback")
            }
          }

          // Fallback para credenciais hardcoded (desenvolvimento/emergência)
          if ((email === '<EMAIL>' || email === '<EMAIL>') && password === 'admin123') {
            if (process.env.NODE_ENV === 'development') {
              console.log("✅ Login admin fallback bem-sucedido")
            }
            return {
              id: 'admin-001',
              email: email,
              name: 'Administrador do Sistema',
              nivelAcesso: 'admin_total',
              ativo: true
            }
          }

          if (email === '<EMAIL>' && password === 'admin123') {
            if (process.env.NODE_ENV === 'development') {
              console.log("✅ Login gerente fallback bem-sucedido")
            }
            return {
              id: 'gerente-001',
              email: '<EMAIL>',
              name: 'Gerente RH',
              nivelAcesso: 'gerente_rh',
              ativo: true
            }
          }

          if (email === '<EMAIL>' && password === 'admin123') {
            if (process.env.NODE_ENV === 'development') {
              console.log("✅ Login supervisor fallback bem-sucedido")
            }
            return {
              id: 'supervisor-001',
              email: '<EMAIL>',
              name: 'Supervisor de Obra',
              nivelAcesso: 'supervisor_obra',
              ativo: true
            }
          }

          // Log apenas em desenvolvimento (sem expor email em produção)
          if (process.env.NODE_ENV === 'development') {
            console.log("❌ Credenciais inválidas")
          }
          return null

        } catch (error) {
          // Log de erro apenas em desenvolvimento
          if (process.env.NODE_ENV === 'development') {
            console.error("❌ Erro na autenticação:", error)
          }
          return null
        }
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 8 * 60 * 60, // 8 horas (será ajustado dinamicamente)
  },
  jwt: {
    maxAge: 8 * 60 * 60, // 8 horas (será ajustado dinamicamente)
  },
  callbacks: {
    async jwt({ token, user, account }) {
      if (user) {
        token.id = user.id
        token.nivelAcesso = (user as any).nivelAcesso
        token.ativo = (user as any).ativo

        // Ajustar tempo de expiração baseado no "Lembrar-me"
        if (account?.rememberMe) {
          // Se "Lembrar-me" marcado: 30 dias
          const thirtyDays = 30 * 24 * 60 * 60
          token.exp = Math.floor(Date.now() / 1000) + thirtyDays
        } else {
          // Padrão: 8 horas
          const eightHours = 8 * 60 * 60
          token.exp = Math.floor(Date.now() / 1000) + eightHours
        }
      }
      return token
    },
    async session({ session, token }) {
      if (token && session.user) {
        (session.user as any).id = token.id as string
        ;(session.user as any).nivelAcesso = token.nivelAcesso as string
        ;(session.user as any).ativo = token.ativo as boolean
      }
      return session
    },
    async redirect({ url, baseUrl }) {
      // Log apenas em desenvolvimento
      if (process.env.NODE_ENV === 'development') {
        console.log("🔄 Redirecionamento:", { url, baseUrl })
      }

      // Se a URL é relativa, adicionar baseUrl
      if (url.startsWith("/")) {
        const redirectUrl = `${baseUrl}${url}`
        if (process.env.NODE_ENV === 'development') {
          console.log("✅ Redirecionamento relativo:", redirectUrl)
        }
        return redirectUrl
      }

      // Se a URL é absoluta e do mesmo domínio, permitir
      try {
        const urlObj = new URL(url)
        const baseUrlObj = new URL(baseUrl)

        if (urlObj.origin === baseUrlObj.origin) {
          if (process.env.NODE_ENV === 'development') {
            console.log("✅ Redirecionamento mesmo domínio:", url)
          }
          return url
        }
      } catch (error) {
        // URL inválida, usar dashboard como fallback
        if (process.env.NODE_ENV === 'development') {
          console.log("⚠️ URL inválida, usando dashboard")
        }
      }

      // Fallback padrão para dashboard
      const dashboardUrl = `${baseUrl}/dashboard`
      if (process.env.NODE_ENV === 'development') {
        console.log("✅ Redirecionamento padrão:", dashboardUrl)
      }
      return dashboardUrl
    }
  },
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  debug: process.env.NODE_ENV === 'development',
  secret: process.env.AUTH_SECRET,
  trustHost: true
})

// Tipos para TypeScript
declare module "next-auth" {
  interface User {
    id: string
    nivelAcesso: string
    ativo: boolean
  }
  
  interface Session {
    user: {
      id: string
      email: string
      name: string
      nivelAcesso: string
      ativo: boolean
    }
  }
}

declare module "@auth/core/jwt" {
  interface JWT {
    id: string
    nivelAcesso: string
    ativo: boolean
  }
}

// Função helper para verificar permissões
export function hasPermission(userLevel: string, requiredLevel: string): boolean {
  const levels = [
    'readonly',
    'operador_biometria', 
    'cliente_vinculado',
    'supervisor_obra',
    'gerente_rh',
    'admin_limitado',
    'admin_total'
  ]
  
  const userIndex = levels.indexOf(userLevel)
  const requiredIndex = levels.indexOf(requiredLevel)
  
  return userIndex >= requiredIndex
}

// Função helper para verificar se usuário está ativo
export function isUserActive(user: { ativo?: boolean } | null | undefined): boolean {
  return user?.ativo === true
}

// Middleware de autenticação para API routes
export async function requireAuth(_req: Request) {
  const session = await auth()
  
  if (!session?.user) {
    throw new Error("Não autenticado")
  }
  
  if (!isUserActive(session.user)) {
    throw new Error("Usuário inativo")
  }
  
  return session.user
}

// Middleware de autorização para API routes
export async function requirePermission(_req: Request, requiredLevel: string) {
  const user = await requireAuth(_req)
  
  if (!hasPermission(user.nivelAcesso, requiredLevel)) {
    throw new Error("Permissão insuficiente")
  }
  
  return user
}
